package modules

import (
	"fmt"
	"igameHttp/types/belatra"
	"math/rand"
	"os"
	"testing"
)

func TestM400159(t *testing.T) {
	// 加载配置
	config, _ := os.ReadFile("../bin/configs/400159.yaml")
	m := m400159{}
	m.Init(config)
	// 生成默认棋盘
	rd := rand.New(rand.NewSource(4))
	grid := make([][]int16, m.Config.InitRow)
	for i := 0; i < m.Config.InitRow; i++ {
		grid[i] = m.RandByWeight.More(m.Config.Column, rd)
	}
	// 基础棋盘 4*6
	for row := range grid {
		fmt.Println(grid[row])
	}
	recursionNorm(&m, rd, grid)

	// fmt.Println()
	// mask, pays := checkLine(&m, grid)
	// fmt.Println(pays)
	// for row := range mask {
	// 	fmt.Println(mask[row])
	// }

	// fmt.Println()
	// newGrid, gridOps := dropSymbols(&m, rd, grid, mask)
	// for row := range newGrid {
	// 	fmt.Println(newGrid[row])
	// }

	// fmt.Println()
	// for row := range gridOps {
	// 	fmt.Println(gridOps[row])
	// }
	// 每触发一次消除则递归一次,叠加棋盘行数
}

func recursionNorm(m *m400159, rd *rand.Rand, grid [][]int16) [][]int16 {
	winmask, pays := checkLine(m, grid)
	newGrid, newops := dropSymbols(m, rd, grid, winmask)
	newGrid = addNewRow(m, rd, newGrid)
	hi := len(grid) - 1
	if hi <= 8 {
		hi++
	} else {
		hi = 8
	}
	iter := belatra.IterInfo{
		Hi:      hi,
		Box:     grid,
		Resbox:  grid,
		Newpos:  newops,
		Win:     pays,
		Winmask: winmask,
		Curwin:  pays,
		Mult:    1,
		Order:   []int16{1, 2, 3, 4, 5, 6},
	}
	fmt.Printf("%+v\n", iter)
	if pays == 0 {
		return newGrid
	}
	return recursionNorm(m, rd, newGrid)
}

func addNewRow(m *m400159, rd *rand.Rand, grid [][]int16) [][]int16 {
	if len(grid) == 0 {
		return [][]int16{{0, 0, 0, 0, 0, 0}}
	}
	if len(grid) >= m.Config.MaxRow {
		return grid
	}
	newGrid := make([][]int16, 0, len(grid)+1)
	newGrid = append(newGrid, m.RandByWeight.More(m.Config.Column, rd))
	newGrid = append(newGrid, grid...)
	return newGrid
}

func checkLine(m *m400159, grid [][]int16) ([][]int16, int32) {
	mask := make([][]int16, len(grid))
	firstIco := []int16{}
	for i := range mask {
		mask[i] = make([]int16, m.Config.Column)
		firstIco = append(firstIco, grid[i][0])
	}
	pays := int32(0)

	// 对每个图标进行检查
	for _, ico := range firstIco {
		consecutiveCount := 0

		for col := 0; col < m.Config.Column; col++ {
			hasIconInCol := false
			for row := 0; row < len(grid); row++ {
				if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
					hasIconInCol = true
					break
				}
			}

			if hasIconInCol {
				consecutiveCount++
			} else {
				break
			}
		}

		if consecutiveCount >= 3 {
			count := 0
			for row := 0; row < len(grid); row++ {
				for col := 0; col < m.Config.Column; col++ {
					if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
						mask[row][col] = 1
						count++
					}
				}
			}
			pays += m.Config.PayoutTable[ico][count]
		}
	}

	return mask, pays
}

func dropSymbols(m *m400159, rd *rand.Rand, grid [][]int16, mask [][]int16) ([][]int16, [][]int16) {
	newGrid := make([][]int16, len(grid))
	gridOps := make([][]int16, len(grid))
	for i := range newGrid {
		newGrid[i] = make([]int16, m.Config.Column)
		gridOps[i] = make([]int16, m.Config.Column)
	}

	// 逐列处理下落逻辑
	for col := 0; col < m.Config.Column; col++ {
		var remainingSymbols []int16
		for row := 0; row < len(grid); row++ {
			// 如果该位置没有被标记为消除（mask[row][col] == 0），则保留该图标
			if mask[row][col] == 0 {
				remainingSymbols = append(remainingSymbols, grid[row][col])
			}
		}

		needNewSymbols := len(grid) - len(remainingSymbols)
		for i := 0; i < needNewSymbols; i++ {
			gridOps[i][col] = 1
		}

		var newSymbols []int16
		for i := 0; i < needNewSymbols; i++ {
			newSymbols = append(newSymbols, m.RandByWeight.One(rd))
		}

		fillIndex := 0

		for _, symbol := range newSymbols {
			newGrid[fillIndex][col] = symbol
			fillIndex++
		}

		for _, symbol := range remainingSymbols {
			newGrid[fillIndex][col] = symbol
			fillIndex++
		}
	}

	return newGrid, gridOps
}
