package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// oneGame
// 游戏ID: 400159
// 游戏名称: MakeItGold
// 作者: pasty
// 生成时间: 2025-08-27 15:00:39

// 配置结构体
type c400159 struct {
	MaxPayout   int64              `yaml:"maxPayout"`   // 最大赔付
	Line        int32              `yaml:"line"`        // 线数
	InitRow     int                `yaml:"initRow"`     // 初始行数
	MaxRow      int                `yaml:"maxRow"`      // 最大行数
	Column      int                `yaml:"column"`      // 列数
	Pattern     [][]basic.Position `yaml:"pattern"`     // 连线模式
	PayoutTable map[int16][]int32  `yaml:"payoutTable"` // 赔付表
	IconWeight  map[int16]int32    `yaml:"iconWeight"`  // 图标权重
	WildIcon    int16              `yaml:"wildIcon"`    // 百搭图标
	ScatterIcon int16              `yaml:"scatterIcon"` // 散布图标
	MinLimit    map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400159])

type m400159 struct {
	Config                      c400159
	RandByWeight                *utils.RandomWeightPicker[int16, int32]
	RandByNoWildWeight          *utils.RandomWeightPicker[int16, int32]
	RandByNoScatterWeight       *utils.RandomWeightPicker[int16, int32]
	RandByNoWildNoScatterWeight *utils.RandomWeightPicker[int16, int32]
}

func (m *m400159) Init(config []byte) {
	m.Config = utils.ParseYAML[c400159](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	noWild := func(icon int16) bool {
		return icon != m.Config.WildIcon
	}
	noScatter := func(icon int16) bool {
		return icon != m.Config.ScatterIcon
	}
	noWildNoScatter := func(icon int16) bool {
		return icon != m.Config.WildIcon && icon != m.Config.ScatterIcon
	}
	m.RandByNoWildWeight = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, noWild))
	m.RandByNoScatterWeight = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, noScatter))
	m.RandByNoWildNoScatterWeight = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, noWildNoScatter))
}

func (m m400159) ID() int32 {
	return 400159
}

func (m m400159) Line() int32 {
	return 20
}

func (m m400159) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400159) Exception(code int32) string {
	s := &games.S400159{}
	return s.Exception(code)
}

func (m *m400159) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400159) genGrid(rd *rand.Rand) [][]int16 {
	grid := make([][]int16, m.Config.InitRow)
	// 第一列不出现 Wild
	for i := 0; i < m.Config.InitRow; i++ {
		grid[i] = make([]int16, m.Config.Column)
		grid[i] = m.RandByWeight.More(m.Config.Column, rd)
		grid[i][0] = m.RandByNoWildWeight.One(rd)
	}
	return grid
}

func (m *m400159) Spin(rd *rand.Rand) basic.ISpin {
	s := &games.S400159{}
	grid := m.genGrid(rd)
	scatt
	page := games.P400159{
		Grid: grid,
		Pays: 0,
	}
	_ = m.recursionNorm(rd, grid, &page)
	s.Pages = append(s.Pages, page)
	s.Pays = page.Pays
	return s
}

func (m *m400159) recursionNorm(rd *rand.Rand, grid [][]int16, page *games.P400159) [][]int16 {
	winmask, pays := m.checkLine(grid, page.Wins)
	newGrid, newops := m.dropSymbols(rd, grid, winmask)
	newGrid = m.addNewRow(rd, newGrid)
	hi := len(grid) - 1
	if hi <= 8 {
		hi++
	} else {
		hi = 8
	}
	markBox := make([][]int16, hi)
	for i := 0; i < hi; i++ {
		markBox[i] = make([]int16, m.Config.Column)
	}
	iter := belatra.IterInfo{
		Hi:      hi,
		Box:     grid,
		Resbox:  grid,
		Newpos:  newops,
		Win:     pays,
		Winmask: winmask,
		Markbox: markBox,
		Curwin:  pays,
		Mult:    1,
		Order:   []int16{1, 2, 3, 4, 5, 6},
	}
	page.IterInfo = append(page.IterInfo, iter)
	page.Pays += pays
	if pays == 0 {
		return newGrid
	}
	return m.recursionNorm(rd, newGrid, page)
}

func (m *m400159) addNewRow(rd *rand.Rand, grid [][]int16) [][]int16 {
	if len(grid) == 0 {
		return [][]int16{{0, 0, 0, 0, 0, 0}}
	}
	if len(grid) >= m.Config.MaxRow {
		return grid
	}
	newGrid := make([][]int16, 0, len(grid)+1)
	// 第一列不出现 Wild
	newRow := m.RandByWeight.More(m.Config.Column, rd)
	newRow[0] = m.RandByNoWildWeight.One(rd)
	newGrid = append(newGrid, newRow)
	newGrid = append(newGrid, grid...)
	return newGrid
}

func (m *m400159) checkLine(grid [][]int16, wins []games.Win400159) ([][]int16, int32) {
	mask := make([][]int16, len(grid))
	firstIco := []int16{}
	for i := range mask {
		mask[i] = make([]int16, m.Config.Column)
		if grid[i][0] == m.Config.ScatterIcon {
			continue
		}
		firstIco = append(firstIco, grid[i][0])
	}
	pays := int32(0)

	// 对每个图标进行检查
	for _, ico := range firstIco {
		consecutiveCount := 0

		for col := 0; col < m.Config.Column; col++ {
			hasIconInCol := false
			for row := 0; row < len(grid); row++ {
				if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
					hasIconInCol = true
					break
				}
			}

			if hasIconInCol {
				consecutiveCount++
			} else {
				break
			}
		}

		if consecutiveCount >= 3 {
			count := 0
			win := games.Win400159{
				Icon: ico,
			}
			for row := 0; row < len(grid); row++ {
				for col := 0; col < m.Config.Column; col++ {
					if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
						mask[row][col] = 1
						win.Positions = append(win.Positions, games.Position400159{Row: row, Col: col})
						count++
					}
				}
			}
			win.Count = count
			wins = append(wins, win)
			pays += m.Config.PayoutTable[ico][count]
		}
	}

	return mask, pays
}

func (m *m400159) dropSymbols(rd *rand.Rand, grid [][]int16, mask [][]int16) ([][]int16, [][]int16) {
	newGrid := make([][]int16, len(grid))
	gridOps := make([][]int16, len(grid))
	for i := range newGrid {
		newGrid[i] = make([]int16, m.Config.Column)
		gridOps[i] = make([]int16, m.Config.Column)
	}

	// 逐列处理下落逻辑
	for col := 0; col < m.Config.Column; col++ {
		var remainingSymbols []int16
		for row := 0; row < len(grid); row++ {
			// 如果该位置没有被标记为消除（mask[row][col] == 0），则保留该图标
			if mask[row][col] == 0 {
				remainingSymbols = append(remainingSymbols, grid[row][col])
			}
		}

		needNewSymbols := len(grid) - len(remainingSymbols)
		for i := 0; i < needNewSymbols; i++ {
			gridOps[i][col] = 1
		}

		var newSymbols []int16
		for i := 0; i < needNewSymbols; i++ {
			if col == 0 {
				newSymbols = append(newSymbols, m.RandByNoWildWeight.One(rd))
			} else {
				newSymbols = append(newSymbols, m.RandByWeight.One(rd))
			}
		}

		fillIndex := 0

		for _, symbol := range newSymbols {
			newGrid[fillIndex][col] = symbol
			fillIndex++
		}

		for _, symbol := range remainingSymbols {
			newGrid[fillIndex][col] = symbol
			fillIndex++
		}
	}

	return newGrid, gridOps
}

func (m *m400159) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400159)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.MaxRow
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400159) Rule(ctx map[string]any) string {
	gs := map[string]any{
		"USED_iterInfo": true,
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":   22806681,
			"VIP_maxWinFreq_small": 397311,
			"arrlimits_winLimitK":  []int{5000, 25000},
			"coeff_energy_win":     5,
			"coinsAssort":          []int{1, 2, 3, 5, 10, 15, 20, 50, 100, 1000},
			"coinsTape":            []any{[]int{21, 20, 19, 18, 21, 18, 20, 19, 21, 20, 20, 18, 18, 24, 19, 21, 20, 23, 21, 20, 20, 21, 19, 22, 23, 19, 24, 18, 19, 19, 21, 19, 19, 20, 19, 20, 19, 18, 21, 21, 21, 19, 19, 19, 19, 23, 18, 22, 22, 18, 19, 18, 21, 20, 21, 21, 18, 21, 20, 20, 20, 20, 24, 21, 20, 19}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"maxIteration":     11,
			"maxWinFreq_big":   78443434,
			"maxWinFreq_small": 576640,
			"minScatters":      []int{4},
			"nSmallPicts":      13,
			"outRates_vipmode": 96.22,
			"sasAdditionalId":  "MGO",
			"sasPaytableId":    "MGO960",
			"scatterIds":       []int{13},
			"statTablo": map[string]any{
				"bigwin":     10,
				"bonus":      7,
				"epicwin":    10,
				"rtp":        96.13,
				"show":       1,
				"volatility": 9,
			},
			"symbolNames": []string{"wild", "book", "hourglass", "flask", "mortar", "wind", "water", "fire", "earth", "a", "k", "q", "j", "scatter", "stone", "stone", "wild", "stone", "coin_copper", "coin_copper", "coin_copper", "coin_copper", "coin_silver", "coin_silver", "coin_silver", "coin_silver", "coin_gold", "coin_gold"},
			"volatility":  4,
			"wildIds":     []int{0, 16},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{25000, 25000, 25000, 25000},
		"betAssortment":   ctx["betAssortment"],
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{{
				"cost":    100,
				"id":      0,
				"prefix2": "_BASE_FG",
				"rtp":     96.23,
			}, {
				"cost":    500,
				"id":      1,
				"prefix2": "_BASE_FG_HOT",
				"rtp":     96.28,
			}, {
				"cost":    9,
				"id":      2,
				"prefix2": "_BASE_GOLD_FLOW_MIX",
				"rtp":     96.27,
			}, {
				"cost":    180,
				"id":      3,
				"prefix2": "_BASE_FG_MIX",
				"rtp":     96.26,
			}},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents": []int{1},
		"dop": map[string]any{
			"base_sc":    0,
			"hi":         4,
			"lasthi":     4,
			"nextfghi":   4,
			"nextfgmult": 1,
			"storageN":   0,
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "FUN",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"firstAward":   []int{0, 0, 0, 0, 8, 10, 12, 0},
				"limit":        50,
				"nestingAward": 3,
				"portions":     3,
			},
			"paytable": []any{[]any{[]int{0, 1}}, []any{[]int{1, 256}, []int{20, 2500}, []int{15, 2000}, []int{10, 1500}, []int{8, 1000}, []int{7, 500}, []int{6, 300}, []int{5, 200}, []int{4, 100}, []int{3, 50}}, []any{[]int{2, 256}, []int{20, 1500}, []int{15, 1000}, []int{10, 500}, []int{8, 300}, []int{7, 250}, []int{6, 200}, []int{5, 100}, []int{4, 50}, []int{3, 30}}, []any{[]int{3, 256}, []int{20, 1000}, []int{15, 500}, []int{10, 300}, []int{8, 250}, []int{7, 200}, []int{6, 100}, []int{5, 50}, []int{4, 30}, []int{3, 20}}, []any{[]int{4, 256}, []int{20, 500}, []int{15, 300}, []int{10, 250}, []int{8, 200}, []int{7, 100}, []int{6, 50}, []int{5, 30}, []int{4, 20}, []int{3, 10}}, []any{[]int{5, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{6, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{7, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{8, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{9, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{10, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{11, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{12, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{13, 8}}, []any{[]int{14, 32}}, []any{[]int{15, 16}}, []any{[]int{16, 1}}, []any{[]int{17, 32}}, []any{[]int{18, 256}, []int{0, 0}}, []any{[]int{19, 256}, []int{0, 0}}, []any{[]int{20, 256}, []int{0, 0}}, []any{[]int{21, 256}, []int{0, 0}}, []any{[]int{22, 256}, []int{0, 0}}, []any{[]int{23, 256}, []int{0, 0}}, []any{[]int{24, 256}, []int{0, 0}}, []any{[]int{25, 256}, []int{0, 0}}, []any{[]int{26, 256}, []int{0, 0}}, []any{[]int{27, 256}, []int{0, 0}}},
		},
		"helpseed":                true,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"last_markbox":            []any{[]int{0, 0, 1, 1, 1, 0}, []int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 2, 2}, []int{0, 0, 0, 0, 2, 2}},
		"last_resbox":             []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"linesAssortment":         []int{20},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   5000,
		"minBetPerGame_cents":     nil,
		"mylinesInfo_v2":          []any{},
		"nlines":                  20,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"setVip_inFreeSpinAlways": -1,
		"startBox":                []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"start_markbox":           []any{[]int{0, 0, 1, 1, 1, 0}, []int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 2, 2}, []int{0, 0, 0, 0, 2, 2}},
		"stopBox":                 []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         25000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 25000,
		},
		"info_adv_games": map[string]any{
			"hint": 0,
			"list": []any{},
			"nnew": 0,
		},
		"newOpenGames": []any{},
		"ss":           map[string]any{},
		"useracc": map[string]any{
			"altcurr":      "fun",
			"amount":       100000,
			"currency":     "FUN",
			"currencyType": 2,
			"currencyUnit": 1,
			"symbol_first": "0",
		},
		"uservars": map[string]any{
			"language": "en",
		},
	}

	b, _ := json.Marshal(gs)
	return string(b)
}

func (m m400159) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	case 2:
		return 50000
	case 3:
		return 900
	case 4:
		return 18000
	case 5:
		return 125
	default:
		return 100
	}
}

func (m m400159) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
